-- =============================================
-- 零工市场和用工信息测试数据
-- 适用于 PublicLaborMarketController.java
-- =============================================

-- 1. 零工市场信息表 (labor_market_info)
-- 修正字段名称，移除不存在的 service_category 字段
INSERT INTO labor_market_info (
    market_id, market_name, market_type, region_code, region_name,
    address, contact_phone, contact_person, description, operating_hours,
    capacity, current_workers, status, is_featured, view_count,
    create_time, update_time, create_by, update_by
) VALUES
-- 推荐零工市场
(1, '北京朝阳零工市场', '综合市场', '建筑装修,家政服务,物流配送', '110105', '朝阳区', 
 '北京市朝阳区建国路88号', '010-85123456', '张经理', 
 '朝阳区最大的综合性零工市场，提供建筑、家政、物流等多种服务', '06:00-20:00', 
 500, 320, '0', 1, 1250, NOW(), NOW(), 'admin', 'admin'),

(2, '上海浦东技能服务中心', '技能服务', '电工维修,水暖安装,家电维修', '310115', '浦东新区', 
 '上海市浦东新区张江高科技园区', '021-58888888', '李主任', 
 '专业技能服务中心，汇聚各类技术工人', '07:00-19:00', 
 300, 180, '0', 1, 980, NOW(), NOW(), 'admin', 'admin'),

(3, '广州天河家政服务市场', '家政服务', '保洁服务,月嫂育儿,老人护理', '440106', '天河区', 
 '广州市天河区体育西路123号', '020-38765432', '王女士', 
 '专业家政服务市场，提供优质家政人员', '08:00-18:00', 
 200, 150, '0', 1, 750, NOW(), NOW(), 'admin', 'admin'),

-- 活跃零工市场
(4, '深圳南山建筑工人市场', '建筑专业', '建筑施工,装修装饰,水电安装', '440305', '南山区', 
 '深圳市南山区科技园南区', '0755-26888999', '陈工', 
 '专业建筑工人聚集地，承接各类建筑项目', '05:30-21:00', 
 800, 650, '0', 0, 2100, NOW(), NOW(), 'admin', 'admin'),

(5, '杭州西湖物流配送中心', '物流配送', '快递配送,货物运输,仓储服务', '330106', '西湖区', 
 '杭州市西湖区文三路456号', '0571-87654321', '刘站长', 
 '专业物流配送服务，覆盖杭州全市', '24小时', 
 400, 280, '0', 0, 1680, NOW(), NOW(), 'admin', 'admin'),

(6, '成都锦江餐饮服务市场', '餐饮服务', '厨师配菜,服务员,清洁工', '510104', '锦江区', 
 '成都市锦江区春熙路789号', '028-86543210', '赵经理', 
 '餐饮行业专业人员聚集地', '06:00-22:00', 
 350, 220, '0', 0, 890, NOW(), NOW(), 'admin', 'admin'),

-- 普通零工市场
(7, '武汉江汉综合服务市场', '综合市场', '各类服务', '420103', '江汉区', 
 '武汉市江汉区中山大道321号', '027-83456789', '孙主管', 
 '提供各类零工服务', '07:00-19:00', 
 250, 120, '0', 0, 450, NOW(), NOW(), 'admin', 'admin'),

(8, '西安雁塔技术服务中心', '技能服务', '维修服务,技术支持', '610113', '雁塔区', 
 '西安市雁塔区高新路654号', '029-88765432', '马师傅', 
 '专业技术服务人员', '08:00-18:00', 
 180, 90, '0', 0, 320, NOW(), NOW(), 'admin', 'admin');

-- 2. 用工信息表 (employment_info)
INSERT INTO employment_info (
    employment_id, title, employment_type, work_category, company_name, contact_person, 
    contact_phone, work_location, region_code, region_name, salary_min, salary_max, 
    salary_type, work_hours, job_description, requirements, education_requirement, 
    experience_requirement, age_requirement, gender_requirement, urgent_level, 
    status, is_featured, view_count, application_count, 
    publish_time, expire_time, create_time, update_time, create_by, update_by
) VALUES 
-- 推荐用工信息
(1, '建筑工地急招瓦工师傅', '临时工', '建筑施工', '北京建工集团', '项目经理王先生', 
 '13800138001', '北京市朝阳区CBD核心区', '110105', '朝阳区', 300, 500, '日薪', 
 '8小时/天', '负责砌墙、贴瓷砖等瓦工作业，要求技术熟练', '有3年以上瓦工经验，能吃苦耐劳', 
 '初中及以上', '3年以上', '25-50岁', '男', '紧急', 
 'published', 1, 156, 23, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW(), 'admin', 'admin'),

(2, '高端家政保姆', '长期工', '家政服务', '金牌家政服务公司', '人事部李女士', 
 '13900139002', '上海市浦东新区陆家嘴', '310115', '浦东新区', 8000, 15000, '月薪', 
 '住家', '照顾老人日常起居，做饭打扫卫生', '有护理经验优先，品行端正，身体健康', 
 '高中及以上', '2年以上', '30-50岁', '女', '一般', 
 'published', 1, 234, 45, NOW(), DATE_ADD(NOW(), INTERVAL 60 DAY), NOW(), NOW(), 'admin', 'admin'),

(3, '物流配送司机', '兼职', '物流配送', '顺丰速运', '招聘专员张先生', 
 '13700137003', '广州市天河区珠江新城', '440106', '天河区', 200, 300, '日薪', 
 '灵活时间', '负责快递包裹配送，熟悉广州路况', '有驾驶证，熟悉本地路况，责任心强', 
 '高中及以上', '1年以上', '22-45岁', '不限', '一般', 
 'published', 1, 189, 67, NOW(), DATE_ADD(NOW(), INTERVAL 45 DAY), NOW(), NOW(), 'admin', 'admin'),

-- 已发布用工信息
(4, '餐厅服务员', '临时工', '餐饮服务', '海底捞火锅', '店长赵女士', 
 '13600136004', '深圳市南山区海岸城', '440305', '南山区', 150, 200, '日薪', 
 '8-10小时', '负责客户接待、点餐服务、清洁卫生', '形象气质佳，服务意识强', 
 '高中及以上', '无要求', '18-35岁', '不限', '一般', 
 'published', 0, 98, 34, NOW(), DATE_ADD(NOW(), INTERVAL 20 DAY), NOW(), NOW(), 'admin', 'admin'),

(5, '电工维修师傅', '长期工', '电工维修', '万科物业', '维修部主管', 
 '13500135005', '杭州市西湖区文三路', '330106', '西湖区', 6000, 9000, '月薪', 
 '8小时/天', '负责小区电路维修、设备维护', '持有电工证，有相关工作经验', 
 '中专及以上', '3年以上', '25-50岁', '男', '一般', 
 'published', 0, 145, 12, NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), NOW(), NOW(), 'admin', 'admin'),

(6, '清洁工', '兼职', '清洁服务', '保利物业', '清洁部负责人', 
 '13400134006', '成都市锦江区春熙路', '510104', '锦江区', 100, 150, '日薪', 
 '4-6小时', '负责办公楼清洁卫生工作', '身体健康，工作认真负责', 
 '小学及以上', '无要求', '18-60岁', '不限', '一般', 
 'published', 0, 67, 8, NOW(), DATE_ADD(NOW(), INTERVAL 15 DAY), NOW(), NOW(), 'admin', 'admin'),

(7, '装修木工', '临时工', '装修装饰', '龙发装饰', '工程部', 
 '13300133007', '武汉市江汉区中山大道', '420103', '江汉区', 250, 400, '日薪', 
 '8-10小时', '负责家装木工作业，包括吊顶、橱柜等', '有木工证，技术熟练', 
 '初中及以上', '5年以上', '25-50岁', '男', '紧急', 
 'published', 0, 201, 19, NOW(), DATE_ADD(NOW(), INTERVAL 25 DAY), NOW(), NOW(), 'admin', 'admin'),

(8, '月嫂', '临时工', '家政服务', '爱心月嫂中心', '业务经理', 
 '13200132008', '西安市雁塔区高新路', '610113', '雁塔区', 8000, 12000, '月薪', 
 '24小时', '照顾产妇和新生儿', '有月嫂证，经验丰富，品行端正', 
 '高中及以上', '2年以上', '30-50岁', '女', '一般', 
 'published', 0, 123, 15, NOW(), DATE_ADD(NOW(), INTERVAL 40 DAY), NOW(), NOW(), 'admin', 'admin');

-- 3. 地区代码表 (region_codes) - 如果需要的话
INSERT INTO region_codes (region_code, region_name, parent_code, level) VALUES 
('110000', '北京市', '0', 1),
('110105', '朝阳区', '110000', 2),
('310000', '上海市', '0', 1),
('310115', '浦东新区', '310000', 2),
('440000', '广东省', '0', 1),
('440106', '天河区', '440000', 2),
('440305', '南山区', '440000', 2),
('330000', '浙江省', '0', 1),
('330106', '西湖区', '330000', 2),
('510000', '四川省', '0', 1),
('510104', '锦江区', '510000', 2),
('420000', '湖北省', '0', 1),
('420103', '江汉区', '420000', 2),
('610000', '陕西省', '0', 1),
('610113', '雁塔区', '610000', 2);

-- 4. 市场类型字典表 (如果需要的话)
INSERT INTO dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, status) VALUES 
('market_type', 1, '综合市场', 'comprehensive', 'market_type', '0'),
('market_type', 2, '建筑专业', 'construction', 'market_type', '0'),
('market_type', 3, '家政服务', 'domestic_service', 'market_type', '0'),
('market_type', 4, '技能服务', 'skill_service', 'market_type', '0'),
('market_type', 5, '物流配送', 'logistics', 'market_type', '0'),
('market_type', 6, '餐饮服务', 'catering', 'market_type', '0');

-- 5. 工作类别字典表
INSERT INTO dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, status) VALUES 
('work_category', 1, '建筑施工', 'construction', 'work_category', '0'),
('work_category', 2, '装修装饰', 'decoration', 'work_category', '0'),
('work_category', 3, '家政服务', 'domestic_service', 'work_category', '0'),
('work_category', 4, '物流配送', 'logistics', 'work_category', '0'),
('work_category', 5, '餐饮服务', 'catering', 'work_category', '0'),
('work_category', 6, '电工维修', 'electrical_repair', 'work_category', '0'),
('work_category', 7, '清洁服务', 'cleaning', 'work_category', '0');

-- 6. 薪资类型字典表
INSERT INTO dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, status) VALUES 
('salary_type', 1, '小时薪', 'hourly', 'salary_type', '0'),
('salary_type', 2, '日薪', 'daily', 'salary_type', '0'),
('salary_type', 3, '月薪', 'monthly', 'salary_type', '0'),
('salary_type', 4, '计件', 'piece_rate', 'salary_type', '0');

-- 7. 用工类型字典表
INSERT INTO dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, status) VALUES 
('employment_type', 1, '临时工', 'temporary', 'employment_type', '0'),
('employment_type', 2, '兼职', 'part_time', 'employment_type', '0'),
('employment_type', 3, '长期工', 'full_time', 'employment_type', '0'),
('employment_type', 4, '季节工', 'seasonal', 'employment_type', '0');
