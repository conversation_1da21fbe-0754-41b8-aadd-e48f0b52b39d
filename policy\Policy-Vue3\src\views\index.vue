<template>
  <div class="welcome-container">
    <div class="welcome-card">
      <div class="welcome-header">
        <h1 class="welcome-title">欢迎登录</h1>
        <p class="welcome-subtitle">Welcome to Our Platform</p>
      </div>

      <div class="welcome-content">
        <div class="welcome-icon">
          <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>

        <div class="welcome-message">
          <h2>感谢您的访问</h2>
          <p>您已成功进入我们的系统平台</p>
          <p>在这里您可以享受到优质的服务体验</p>
        </div>

        <div class="welcome-features">
          <div class="feature-item">
            <div class="feature-icon">🚀</div>
            <span>高效便捷</span>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔒</div>
            <span>安全可靠</span>
          </div>
          <div class="feature-item">
            <div class="feature-icon">💡</div>
            <span>智能服务</span>
          </div>
        </div>

        <div class="welcome-actions">
          <button class="btn-primary" @click="startExploring">开始探索</button>
          <button class="btn-secondary" @click="viewGuide">查看指南</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const currentTime = ref(new Date().toLocaleString('zh-CN'))

// 方法
const startExploring = () => {
  console.log('开始探索系统功能')
  // 这里可以添加导航到其他页面的逻辑
}

const viewGuide = () => {
  console.log('查看使用指南')
  // 这里可以添加显示帮助文档的逻辑
}

// 更新时间
setInterval(() => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}, 1000)
</script>

<style scoped>
.welcome-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.welcome-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 600px;
  width: 100%;
  text-align: center;
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-header {
  margin-bottom: 30px;
}

.welcome-title {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 10px;
  font-weight: 700;
}

.welcome-subtitle {
  font-size: 1.1rem;
  color: #718096;
  margin: 0;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.welcome-icon {
  display: flex;
  justify-content: center;
}

.welcome-message h2 {
  font-size: 1.5rem;
  color: #2d3748;
  margin-bottom: 15px;
}

.welcome-message p {
  color: #4a5568;
  line-height: 1.6;
  margin: 8px 0;
}

.welcome-features {
  display: flex;
  justify-content: space-around;
  gap: 20px;
  margin: 20px 0;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 5px;
}

.feature-item span {
  color: #4a5568;
  font-weight: 500;
}

.welcome-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(135deg, #4F46E5, #7C3AED);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #4F46E5;
  border: 2px solid #4F46E5;
}

.btn-secondary:hover {
  background: #4F46E5;
  color: white;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-card {
    padding: 30px 20px;
    margin: 10px;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .welcome-features {
    flex-direction: column;
    gap: 15px;
  }

  .welcome-actions {
    flex-direction: column;
  }

  .btn-primary, .btn-secondary {
    width: 100%;
  }
}
</style>